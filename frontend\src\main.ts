import './style.css'

// API基础URL - 使用相对路径，通过Nginx代理
const API_BASE_URL = '/api_compare/api'

// 接口类型定义
interface Language {
  code: string
  name: string
}

interface TTSResponse {
  success: boolean
  audioBase64?: string
  translatedText?: string
  message?: string
}

interface CompareResponse {
  success: boolean
  score?: number
  characterConsistency?: number
  detail?: string
  message?: string
}

interface ExcelCompareResponse {
  success: boolean
  results?: Array<{
    rowIndex: number
    skipped: boolean
    reason?: string
    originalText?: string
    recognizedText?: string
    score?: number
    characterConsistency?: number
    detail?: string
  }>
  totalRows?: number
  processedRows?: number
  skippedRows?: number
  message?: string
}

// DOM元素获取
const tabButtons = document.querySelectorAll('.tab-button')
const tabPanels = document.querySelectorAll('.tab-panel')
const languageSelect = document.querySelector('#language-select') as HTMLDivElement
const textInput = document.querySelector('#text-input') as HTMLTextAreaElement
const generateBtn = document.querySelector('#generate-btn') as HTMLButtonElement
const progressContainer = document.querySelector('#progress-container') as HTMLDivElement
const progressFill = document.querySelector('#progress-fill') as HTMLDivElement
const progressText = document.querySelector('#progress-text') as HTMLSpanElement
const audioResults = document.querySelector('#audio-results') as HTMLDivElement
const originalText = document.querySelector('#original-text') as HTMLTextAreaElement
const recognizedTextsList = document.querySelector('#recognized-texts-list') as HTMLDivElement
const addRecognizedTextBtn = document.querySelector('#add-recognized-text') as HTMLButtonElement
const compareBtn = document.querySelector('#compare-btn') as HTMLButtonElement

// Excel相关元素
const uploadArea = document.querySelector('#upload-area') as HTMLDivElement
const excelUpload = document.querySelector('#excel-upload') as HTMLInputElement
const fileInfo = document.querySelector('#file-info') as HTMLDivElement
const fileName = document.querySelector('.file-name') as HTMLSpanElement
const removeFileBtn = document.querySelector('.btn-remove-file') as HTMLButtonElement
const startCompareBtn = document.querySelector('#start-compare-btn') as HTMLButtonElement
const excelProgressContainer = document.querySelector('#excel-progress-container') as HTMLDivElement
const excelProgressFill = document.querySelector('#excel-progress-fill') as HTMLDivElement
const excelProgressText = document.querySelector('#excel-progress-text') as HTMLSpanElement
const progressDetailsText = document.querySelector('#progress-details-text') as HTMLSpanElement
const excelResults = document.querySelector('#excel-results') as HTMLDivElement
const resultsSummaryText = document.querySelector('#results-summary-text') as HTMLSpanElement
const downloadExcelBtn = document.querySelector('#download-excel-btn') as HTMLButtonElement

// 全局变量
let selectedLanguages: Language[] = []
let allLanguages: Language[] = []
let recognizedTextIndex = 0
let selectedFile: File | null = null
let excelCompareResults: any = null

// 并发控制
const MAX_CONCURRENT = 10
class Queue {
  private queue: (() => Promise<void>)[] = []
  private running = 0

  async add(task: () => Promise<void>) {
    this.queue.push(task)
    await this.runNext()
  }

  private async runNext() {
    if (this.running >= MAX_CONCURRENT || this.queue.length === 0) return
    
    this.running++
    const task = this.queue.shift()!
    try {
      await task()
    } finally {
      this.running--
      await this.runNext()
    }
  }
}
const taskQueue = new Queue()

// Tab切换功能
tabButtons.forEach(button => {
  button.addEventListener('click', () => {
    const tabId = button.getAttribute('data-tab')

    // 移除所有active类
    tabButtons.forEach(btn => btn.classList.remove('active'))
    tabPanels.forEach(panel => panel.classList.remove('active'))

    // 添加active类到当前选中的tab
    button.classList.add('active')
    document.querySelector(`#${tabId}-tab`)?.classList.add('active')
  })
})

// 加载语言列表
async function loadLanguages() {
  try {
    const response = await fetch(`${API_BASE_URL}/languages`)
    allLanguages = await response.json()

    // 初始化多选语言选择器
    initLanguageSelector()
    updateSelectedLanguagesDisplay()
  } catch (error) {
    console.error('加载语言列表失败:', error)
    const placeholder = languageSelect.querySelector('.placeholder')
    if (placeholder) {
      placeholder.textContent = '加载失败，请刷新重试'
    }
  }
}

// 初始化语言选择器
function initLanguageSelector() {
  const selectedLanguagesDiv = languageSelect.querySelector('.selected-languages') as HTMLDivElement
  const dropdown = document.querySelector('#language-dropdown') as HTMLDivElement

  // 清空下拉列表
  dropdown.innerHTML = ''
  
  // 添加搜索框和全选/反选功能
  const searchContainer = document.createElement('div')
  searchContainer.className = 'language-search-container'
  
  const searchInput = document.createElement('input')
  searchInput.className = 'language-search-input'
  searchInput.placeholder = '搜索语言...'
  searchContainer.appendChild(searchInput)
  
  const selectContainer = document.createElement('div')
  selectContainer.className = 'select-all-container'
  
  const selectAllBtn = document.createElement('button')
  selectAllBtn.className = 'select-all-btn'
  selectAllBtn.textContent = '全选'
  
  const deselectAllBtn = document.createElement('button')
  deselectAllBtn.className = 'deselect-all-btn'
  deselectAllBtn.textContent = '全不选'
  
  selectContainer.appendChild(selectAllBtn)
  selectContainer.appendChild(deselectAllBtn)
  searchContainer.appendChild(selectContainer)
  
  dropdown.appendChild(searchContainer)

  const optionsContainer = document.createElement('div')
  optionsContainer.className = 'language-options-container'
  dropdown.appendChild(optionsContainer)

  // 添加语言选项
  function renderLanguageOptions(filter = '') {
    optionsContainer.innerHTML = ''
    allLanguages
      .filter(lang => lang.name.toLowerCase().includes(filter.toLowerCase()))
      .forEach(lang => {
        const option = document.createElement('div')
        option.className = 'language-option'
        option.textContent = lang.name
        option.dataset.code = lang.code

        option.addEventListener('click', () => {
          toggleLanguageSelection(lang)
        })

        optionsContainer.appendChild(option)
      })
  }

  renderLanguageOptions()

  // 搜索功能
  searchInput.addEventListener('input', (e) => {
    const filter = (e.target as HTMLInputElement).value
    renderLanguageOptions(filter)
    updateLanguageDropdownState()
  })

  // 全选功能
  selectAllBtn.addEventListener('click', (e) => {
    e.stopPropagation()
    const visibleOptions = [...optionsContainer.querySelectorAll('.language-option')] as HTMLDivElement[]
    const visibleLanguages = visibleOptions.map(option => ({
      code: option.dataset.code!,
      name: option.textContent!
    }))
    visibleLanguages.forEach(lang => {
      if (!selectedLanguages.some(selected => selected.code === lang.code)) {
        selectedLanguages.push(lang)
      }
    })
    updateSelectedLanguagesDisplay()
    updateLanguageDropdownState()
  })

  // 全不选功能
  deselectAllBtn.addEventListener('click', (e) => {
    e.stopPropagation()
    const visibleOptions = [...optionsContainer.querySelectorAll('.language-option')] as HTMLDivElement[]
    const visibleCodes = visibleOptions.map(option => option.dataset.code!)
    selectedLanguages = selectedLanguages.filter(lang => !visibleCodes.includes(lang.code))
    updateSelectedLanguagesDisplay()
    updateLanguageDropdownState()
  })

  // 点击选择器显示/隐藏下拉列表
  selectedLanguagesDiv.addEventListener('click', () => {
    const isVisible = dropdown.style.display === 'block'
    dropdown.style.display = isVisible ? 'none' : 'block'
    selectedLanguagesDiv.classList.toggle('active', !isVisible)
  })

  // 点击外部关闭下拉列表
  document.addEventListener('click', (e) => {
    if (!languageSelect.contains(e.target as Node)) {
      dropdown.style.display = 'none'
      selectedLanguagesDiv.classList.remove('active')
    }
  })
}

// 切换语言选择
function toggleLanguageSelection(language: Language) {
  const index = selectedLanguages.findIndex(lang => lang.code === language.code)

  if (index > -1) {
    // 移除语言
    selectedLanguages.splice(index, 1)
  } else {
    // 添加语言
    selectedLanguages.push(language)
  }

  updateSelectedLanguagesDisplay()
  updateLanguageDropdownState()
}

// 更新选中语言显示
function updateSelectedLanguagesDisplay() {
  const selectedLanguagesDiv = languageSelect.querySelector('.selected-languages') as HTMLDivElement

  if (selectedLanguages.length === 0) {
    selectedLanguagesDiv.innerHTML = '<span class="placeholder">请选择语言（可多选）</span>'
  } else {
    selectedLanguagesDiv.innerHTML = ''
    selectedLanguages.forEach(lang => {
      const tag = document.createElement('div')
      tag.className = 'language-tag'
      tag.innerHTML = `
        <span>${lang.name}</span>
        <span class="remove" data-code="${lang.code}">×</span>
      `

      // 添加移除事件
      const removeBtn = tag.querySelector('.remove') as HTMLSpanElement
      removeBtn.addEventListener('click', (e) => {
        e.stopPropagation()
        toggleLanguageSelection(lang)
      })

      selectedLanguagesDiv.appendChild(tag)
    })
  }
}

// 更新下拉列表状态
function updateLanguageDropdownState() {
  const dropdown = document.querySelector('#language-dropdown') as HTMLDivElement
  const options = dropdown.querySelectorAll('.language-option')

  options.forEach(option => {
    const htmlOption = option as HTMLDivElement
    const code = htmlOption.dataset.code
    const isSelected = selectedLanguages.some(lang => lang.code === code)
    option.classList.toggle('selected', isSelected)
  })
}

// 批量TTS生成功能
generateBtn.addEventListener('click', async () => {
  const text = textInput.value.trim()

  if (selectedLanguages.length === 0) {
    alert('请至少选择一种语言')
    return
  }

  if (!text) {
    alert('请输入中文文本')
    return
  }

  // 显示进度条
  progressContainer.style.display = 'block'
  audioResults.innerHTML = ''
  generateBtn.disabled = true

  const totalLanguages = selectedLanguages.length
  let completedCount = 0

  // 更新进度
  function updateProgress() {
    const percentage = Math.round((completedCount / totalLanguages) * 100)
    progressFill.style.width = `${percentage}%`
    progressText.textContent = `${percentage}%`
  }

  updateProgress()

  // 并发处理所有语言
  const promises = selectedLanguages.map(async (language) => {
    try {
      const response = await fetch(`${API_BASE_URL}/tts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          language: language.code,
          text: text,
          needTranslation: true
        })
      })

      const result: TTSResponse = await response.json()

      if (result.success && result.audioBase64) {
        // 创建音频结果项
        createAudioResultItem(language, result.translatedText || text, result.audioBase64)
      } else {
        createErrorResultItem(language, result.message || '生成失败')
      }
    } catch (error) {
      console.error(`${language.name} TTS生成失败:`, error)
      createErrorResultItem(language, '网络错误')
    } finally {
      completedCount++
      updateProgress()
    }
  })

  // 等待所有请求完成
  await Promise.all(promises)

  generateBtn.disabled = false

  // 3秒后隐藏进度条
  setTimeout(() => {
    progressContainer.style.display = 'none'
  }, 3000)
})

// 创建音频结果项
function createAudioResultItem(language: Language, translatedText: string, audioBase64: string) {
  const audioBlob = base64ToBlob(audioBase64, 'audio/mpeg')
  const audioUrl = URL.createObjectURL(audioBlob)

  const resultItem = document.createElement('div')
  resultItem.className = 'audio-result-item'
  resultItem.innerHTML = `
    <div class="audio-result-header">
      <div class="language-name">${language.name}</div>
    </div>
    <div class="audio-result-content">
      <div class="translated-text-section">
        <div class="label">翻译后的文本：</div>
        <div class="translated-text-content">
          ${translatedText}
          <button class="btn-copy" title="复制翻译文本">📋</button>
        </div>
      </div>
      <div class="audio-controls">
        <audio controls class="audio-player-small">
          <source src="${audioUrl}" type="audio/mpeg">
          您的浏览器不支持音频播放。
        </audio>
        <button class="btn btn-secondary download-btn">下载音频</button>
      </div>
    </div>
  `

  // 添加复制功能
  const copyBtn = resultItem.querySelector('.btn-copy') as HTMLButtonElement
  copyBtn.addEventListener('click', async () => {
    try {
      await navigator.clipboard.writeText(translatedText)
      const originalText = copyBtn.textContent
      copyBtn.textContent = '✅'
      setTimeout(() => {
        copyBtn.textContent = originalText
      }, 1000)
    } catch (error) {
      console.error('复制失败:', error)
      alert('复制失败，请手动选择文本复制')
    }
  })

  // 添加下载功能
  const downloadBtn = resultItem.querySelector('.download-btn') as HTMLButtonElement
  downloadBtn.addEventListener('click', () => {
    const link = document.createElement('a')
    link.href = audioUrl
    link.download = `tts_${language.code}_${Date.now()}.mp3`
    link.click()
  })

  audioResults.appendChild(resultItem)
}

// 创建错误结果项
function createErrorResultItem(language: Language, errorMessage: string) {
  const resultItem = document.createElement('div')
  resultItem.className = 'audio-result-item'
  resultItem.style.background = 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)'
  resultItem.style.borderColor = '#f5c6cb'
  resultItem.innerHTML = `
    <div class="audio-result-header">
      <div class="language-name" style="color: #721c24;">${language.name}</div>
    </div>
    <div class="audio-result-content">
      <div style="color: #721c24; text-align: center; padding: 20px;">
        ❌ ${errorMessage}
      </div>
    </div>
  `

  audioResults.appendChild(resultItem)
}

// Base64转Blob
function base64ToBlob(base64: string, mimeType: string): Blob {
  const byteCharacters = atob(base64)
  const byteNumbers = new Array(byteCharacters.length)

  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i)
  }

  const byteArray = new Uint8Array(byteNumbers)
  return new Blob([byteArray], { type: mimeType })
}

// 添加语音识别文本输入框
addRecognizedTextBtn.addEventListener('click', () => {
  recognizedTextIndex++
  const newItem = createRecognizedTextItem(recognizedTextIndex)
  recognizedTextsList.appendChild(newItem)
})

// 创建语音识别文本输入项
function createRecognizedTextItem(index: number): HTMLDivElement {
  const item = document.createElement('div')
  item.className = 'recognized-text-item'
  item.dataset.index = index.toString()

  item.innerHTML = `
    <div class="text-input-section">
      ${index > 0 ? '<button class="btn-remove" title="删除此项">➖</button>' : ''}
      <textarea class="form-control recognized-text-input" rows="3" placeholder="请输入语音识别后的文本..."></textarea>
    </div>
    <div class="result-section">
      <span style="color: #6c757d;">等待比对...</span>
    </div>
  `

  // 添加删除功能（除了第一项）
  if (index > 0) {
    const removeBtn = item.querySelector('.btn-remove') as HTMLButtonElement
    removeBtn.addEventListener('click', () => {
      item.remove()
    })
  }

  return item
}

// 单个文本比对函数
async function compareText(element: HTMLDivElement, original: string, recognizedText: string, index: number) {
  const resultSection = element.querySelector('.result-section') as HTMLDivElement
  resultSection.className = 'result-section loading'
  resultSection.innerHTML = '<span style="color: #856404;">比对中...</span>'

  try {
    const response = await fetch(`${API_BASE_URL}/compare`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ originalText: original, recognizedText })
    })

    const result: CompareResponse = await response.json()

    if (result.success && typeof result.score === 'number' && result.detail) {
      // 显示比对结果
      resultSection.className = 'result-section completed'

      // 计算总体评分的样式类
      let scoreClass = 'score-poor'
      if (result.score >= 90) {
        scoreClass = 'score-excellent'
      } else if (result.score >= 70) {
        scoreClass = 'score-good'
      } else if (result.score >= 30) {
        scoreClass = 'score-fair'
      }

      // 计算文本一致性的样式类
      let consistencyClass = 'consistency-low'
      const consistency = result.characterConsistency ?? 0
      if (consistency >= 90) {
        consistencyClass = 'consistency-high'
      } else if (consistency >= 70) {
        consistencyClass = 'consistency-medium'
      } else if (consistency >= 30) {
        consistencyClass = 'consistency-low'
      } else {
        consistencyClass = 'consistency-very-low'
      }

      resultSection.innerHTML = `
        <div class="compare-result-inline">
          <div class="score-display ${scoreClass}">${result.score}</div>
          <div class="character-consistency ${consistencyClass}">
            <span class="consistency-label">文本一致性：</span>
            <span class="consistency-value">${result.characterConsistency ?? '计算中...'}${result.characterConsistency ? '%' : ''}</span>
          </div>
          <div class="detail-display">${result.detail}</div>
        </div>
      `
    } else {
      resultSection.className = 'result-section'
      resultSection.innerHTML = `<span style="color: #dc3545;">❌ ${result.message || '比对失败'}</span>`
    }
  } catch (error) {
    console.error(`文本比对失败 (${index}):`, error)
    resultSection.className = 'result-section'
    resultSection.innerHTML = '<span style="color: #dc3545;">❌ 网络错误</span>'
  }
}

// 批量文本比对功能
compareBtn.addEventListener('click', async () => {
  const original = originalText.value.trim()
  const recognizedItems = recognizedTextsList.querySelectorAll('.recognized-text-item')

  if (!original) {
    alert('请输入原文')
    return
  }

  // 获取所有非空的语音识别文本
  const recognizedTexts: { element: HTMLDivElement, text: string, index: number }[] = []
  recognizedItems.forEach((item, index) => {
    const textarea = item.querySelector('.recognized-text-input') as HTMLTextAreaElement
    const text = textarea.value.trim()
    if (text) {
      recognizedTexts.push({
        element: item as HTMLDivElement,
        text: text,
        index: index
      })
    }
  })

  if (recognizedTexts.length === 0) {
    alert('请至少输入一条语音识别文本')
    return
  }

  compareBtn.disabled = true

  // 使用并发队列处理文本比对
  const comparePromises: Promise<void>[] = []
  for (const recognized of recognizedTexts) {
    comparePromises.push(
      taskQueue.add(async () => {
        await compareText(recognized.element, original, recognized.text, recognized.index)
      })
    )
  }

  await Promise.all(comparePromises)
  compareBtn.disabled = false

  // 已经在队列中处理完所有比对，不需要额外的代码
})

// Excel文件上传功能
function initExcelUpload() {
  // 点击上传区域触发文件选择
  uploadArea.addEventListener('click', () => {
    excelUpload.click()
  })

  // 拖拽功能
  uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault()
    uploadArea.classList.add('dragover')
  })

  uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover')
  })

  uploadArea.addEventListener('drop', (e) => {
    e.preventDefault()
    uploadArea.classList.remove('dragover')

    const files = e.dataTransfer?.files
    if (files && files.length > 0) {
      handleFileSelect(files[0])
    }
  })

  // 文件选择
  excelUpload.addEventListener('change', (e) => {
    const files = (e.target as HTMLInputElement).files
    if (files && files.length > 0) {
      handleFileSelect(files[0])
    }
  })

  // 移除文件
  removeFileBtn.addEventListener('click', () => {
    clearSelectedFile()
  })

  // 开始校对
  startCompareBtn.addEventListener('click', () => {
    if (selectedFile) {
      startExcelCompare()
    }
  })

  // 下载结果
  downloadExcelBtn.addEventListener('click', () => {
    if (excelCompareResults) {
      downloadResultExcel()
    }
  })
}

// 处理文件选择
function handleFileSelect(file: File) {
  // 检查文件类型
  const allowedTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel'
  ]

  if (!allowedTypes.includes(file.type)) {
    alert('请选择Excel文件（.xlsx 或 .xls）')
    return
  }

  // 检查文件大小（10MB）
  if (file.size > 10 * 1024 * 1024) {
    alert('文件大小不能超过10MB')
    return
  }

  selectedFile = file

  // 显示文件信息
  fileName.textContent = file.name
  fileInfo.style.display = 'flex'
  uploadArea.style.display = 'none'

  // 启用开始校对按钮
  startCompareBtn.disabled = false

  // 隐藏结果区域
  excelResults.style.display = 'none'
}

// 清除选中的文件
function clearSelectedFile() {
  selectedFile = null
  excelUpload.value = ''
  fileInfo.style.display = 'none'
  uploadArea.style.display = 'block'
  startCompareBtn.disabled = true
  excelResults.style.display = 'none'
  excelProgressContainer.style.display = 'none'
}

// 开始Excel批量比对
async function startExcelCompare() {
  if (!selectedFile) return

  startCompareBtn.disabled = true
  excelProgressContainer.style.display = 'block'
  excelResults.style.display = 'none'

  // 重置进度
  excelProgressFill.style.width = '0%'
  excelProgressText.textContent = '0%'
  progressDetailsText.textContent = '正在上传文件...'

  try {
    // 创建FormData
    const formData = new FormData()
    formData.append('excel', selectedFile)

    // 上传并处理Excel文件
    const response = await fetch(`${API_BASE_URL}/excel-compare`, {
      method: 'POST',
      body: formData
    })

    const result: ExcelCompareResponse = await response.json()

    if (result.success && result.results) {
      excelCompareResults = result

      // 更新进度为100%
      excelProgressFill.style.width = '100%'
      excelProgressText.textContent = '100%'
      progressDetailsText.textContent = '校对完成！'

      // 显示结果
      showExcelResults(result)

      // 3秒后隐藏进度条
      setTimeout(() => {
        excelProgressContainer.style.display = 'none'
      }, 3000)
    } else {
      throw new Error(result.message || '处理失败')
    }

  } catch (error) {
    console.error('Excel批量比对失败:', error)
    progressDetailsText.textContent = '处理失败: ' + (error as Error).message
    alert('Excel处理失败: ' + (error as Error).message)
  } finally {
    startCompareBtn.disabled = false
  }
}

// 显示Excel比对结果
function showExcelResults(result: ExcelCompareResponse) {
  if (!result.results) return

  const summary = `
    总计 ${result.totalRows} 行数据，
    成功处理 ${result.processedRows} 行，
    跳过 ${result.skippedRows} 行
  `

  resultsSummaryText.textContent = summary
  excelResults.style.display = 'block'
}

// 下载结果Excel
async function downloadResultExcel() {
  if (!excelCompareResults || !excelCompareResults.results) return

  try {
    // 准备Excel数据
    const headers = ['语言', '原文', 'ASR识别的文本', '文本一致性', '综合评分', '评分详情']
    const data: any[][] = []

    excelCompareResults.results.forEach((result: any) => {
      if (result.skipped) {
        // 跳过的行，只填入原有数据
        data.push([
          '', // 语言
          result.originalText || '',
          result.recognizedText || '',
          result.reason || '跳过',
          '',
          ''
        ])
      } else {
        // 成功处理的行
        data.push([
          '', // 语言
          result.originalText || '',
          result.recognizedText || '',
          result.characterConsistency ? `${result.characterConsistency}%` : '',
          result.score || '',
          result.detail || ''
        ])
      }
    })

    // 发送请求生成Excel
    const response = await fetch(`${API_BASE_URL}/generate-excel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ headers, data })
    })

    if (response.ok) {
      // 下载文件
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `excel_compare_result_${Date.now()}.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)
    } else {
      throw new Error('生成Excel文件失败')
    }

  } catch (error) {
    console.error('下载Excel失败:', error)
    alert('下载失败: ' + (error as Error).message)
  }
}

// 页面初始化
function initApp() {
  loadLanguages()

  // 初始化第一个语音识别文本输入框
  const firstItem = createRecognizedTextItem(0)
  recognizedTextsList.appendChild(firstItem)

  // 初始化Excel上传功能
  initExcelUpload()
}

// 确保DOM加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp)
} else {
  initApp()
}

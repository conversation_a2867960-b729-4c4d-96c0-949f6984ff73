// 创建测试Excel文件的脚本
const XLSX = require('xlsx');

// 测试数据
const testData = [
  ['语言', '原文', 'ASR识别的文本', '文本一致性', '综合评分', '评分详情'],
  ['中文', '你好明天见', '你好明天见吗？', '', '', ''],
  ['中文', '今天天气很好', '今天天气很好', '', '', ''],
  ['中文', '我要去超市买东西', '我要去超市买点东西', '', '', ''],
  ['中文', '请问现在几点了', '请问现在几点', '', '', ''],
  ['中文', '谢谢你的帮助', '谢谢你的帮助', '', '', '']
];

// 创建工作簿
const workbook = XLSX.utils.book_new();

// 创建工作表
const worksheet = XLSX.utils.aoa_to_sheet(testData);

// 添加工作表到工作簿
XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

// 写入文件
XLSX.writeFile(workbook, 'test_excel_compare.xlsx');

console.log('测试Excel文件已创建: test_excel_compare.xlsx');

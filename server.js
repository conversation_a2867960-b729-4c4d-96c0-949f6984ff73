const express = require('express')
const cors = require('cors')
const multer = require('multer')
const XLSX = require('xlsx')
const path = require('path')
const fs = require('fs')
const { getAllLanguages, getLanguageProvider, getAliLanguageCode, isLanguageSupported } = require('./languages')
const { getValidToken } = require('./tokenManager')
const { synthesizeTextAli, aliVoices } = require('./tts_ali')
const { generateTTS } = require('./tts_openai')
const { chatCompletion } = require('./llm')

const app = express()
const PORT = 5578

// 配置multer用于文件上传
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB限制
  },
  fileFilter: (req, file, cb) => {
    // 只允许Excel文件
    const allowedMimes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ]
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true)
    } else {
      cb(new Error('只支持Excel文件(.xlsx, .xls)'))
    }
  }
})

// 确保uploads目录存在
if (!fs.existsSync('uploads')) {
  fs.mkdirSync('uploads')
}

// 中间件
app.use(cors())
app.use(express.json())

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err)
  res.status(500).json({
    success: false,
    message: '服务器内部错误'
  })
})

// 获取语言列表接口
app.get('/api/languages', (req, res) => {
  try {
    const languages = getAllLanguages()
    res.json(languages)
  } catch (error) {
    console.error('获取语言列表失败:', error)
    res.status(500).json({
      success: false,
      message: '获取语言列表失败'
    })
  }
})

// 翻译接口
app.post('/api/translate', async (req, res) => {
  try {
    const { text, targetLanguage } = req.body

    // 参数验证
    if (!text || !targetLanguage) {
      return res.status(400).json({
        success: false,
        message: '文本和目标语言参数不能为空'
      })
    }

    // 构建翻译提示词
    const languageNames = {
      'zh_hans': '简体中文',
      'zh_hant': '繁体中文',
      'en': '英语',
      'ja': '日语',
      'ko': '韩语',
      'de': '德语',
      'es': '西班牙语',
      'ru': '俄语',
      'fr': '法语',
      'it': '意大利语',
      'pt': '葡萄牙语',
      'ar': '阿拉伯语',
      'hi': '印地语',
      'th': '泰语',
      'vi': '越南语',
      'tr': '土耳其语',
      'pl': '波兰语',
      'nl': '荷兰语',
      'sv': '瑞典语',
      'da': '丹麦语',
      'no': '挪威语',
      'fi': '芬兰语'
    }

    const targetLanguageName = languageNames[targetLanguage] || targetLanguage

    const prompt = `请将以下中文文本翻译成${targetLanguageName}，只返回翻译结果，不要包含其他内容：

${text}`

    // 调用LLM进行翻译
    const translatedText = await chatCompletion(prompt)

    res.json({
      success: true,
      translatedText: translatedText.trim()
    })

  } catch (error) {
    console.error('翻译失败:', error)
    res.status(500).json({
      success: false,
      message: '翻译失败: ' + error.message
    })
  }
})

// TTS生成接口
app.post('/api/tts', async (req, res) => {
  try {
    const { language, text, needTranslation } = req.body

    // 参数验证
    if (!language || !text) {
      return res.status(400).json({
        success: false,
        message: '语言和文本参数不能为空'
      })
    }

    // 检查语言是否支持
    if (!isLanguageSupported(language)) {
      return res.status(400).json({
        success: false,
        message: '不支持的语言'
      })
    }

    let finalText = text
    let translatedText = null

    // 如果需要翻译，先进行翻译
    if (needTranslation) {
      try {
        const translateResponse = await fetch('http://localhost:5578/api/translate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            text: text,
            targetLanguage: language
          })
        })

        const translateResult = await translateResponse.json()
        if (translateResult.success) {
          finalText = translateResult.translatedText
          translatedText = translateResult.translatedText
        } else {
          throw new Error('翻译失败')
        }
      } catch (translateError) {
        console.error('翻译过程出错:', translateError)
        return res.status(500).json({
          success: false,
          message: '翻译失败，请重试'
        })
      }
    }

    const provider = getLanguageProvider(language)
    let audioBase64

    if (provider === 'ali') {
      // 使用阿里云TTS
      const aliLangCode = getAliLanguageCode(language)
      const token = await getValidToken()

      // 获取对应语言的默认音色
      let voice
      if (aliVoices[aliLangCode] && aliVoices[aliLangCode].default) {
        voice = aliVoices[aliLangCode].default
      } else {
        voice = 'xiaoyun' // 默认音色
      }

      audioBase64 = await synthesizeTextAli({
        text: finalText,
        voice,
        token,
        languageCode: aliLangCode
      })
    } else if (provider === 'openai') {
      // 使用OpenAI TTS
      audioBase64 = await generateTTS(finalText)
    } else {
      return res.status(400).json({
        success: false,
        message: '未知的TTS提供商'
      })
    }

    const response = {
      success: true,
      audioBase64
    }

    // 如果进行了翻译，返回翻译后的文本
    if (translatedText) {
      response.translatedText = translatedText
    }

    res.json(response)

  } catch (error) {
    console.error('TTS生成失败:', error)
    res.status(500).json({
      success: false,
      message: 'TTS生成失败: ' + error.message
    })
  }
})

// 文本比对接口
app.post('/api/compare', async (req, res) => {
  try {
    const { originalText, recognizedText } = req.body

    // 参数验证
    if (!originalText || !recognizedText) {
      return res.status(400).json({
        success: false,
        message: '原文和语音识别文本不能为空'
      })
    }

    // 构建提示词
    const prompt = `你是一个专业的文本比对评估专家。请比较以下两段文本并提供详细的分析：

原文：
${originalText}

语音识别文本：
${recognizedText}

请提供以下三个方面的评估：

1. 总体评分(score)：
- 90分以上：很完美，基本上跟原文差别不大
- 70分以上：及格，虽然跟原文有出入但是不影响理解
- 30分以上：不及格，已经影响到理解了
- 30分以下：完全牛头不对马嘴

2. 字数一致性(characterConsistency)：
- 计算方法：1 - (|原文字数 - 识别文本字数| / Math.max(原文字数, 识别文本字数)) * 100
- 结果应该是0-100之间的数字，表示字数匹配的百分比
- 两段文本字数完全相同时为100
- 字数差异越大，这个值越小

3. 详细分析(detail)：
- 描述文本差异的具体表现
- 指出主要的问题点
- 给出整体的比对结论

请按以下格式返回JSON结果（不要包含其他内容）：
{
  "score": 88,
  "characterConsistency": 95,
  "detail": "相比原文，语音识别后的文本在一些意思描述有点不清晰，但总体来说是正确的"
}
    `
    // 调用LLM进行比对
    const llmResponse = await chatCompletion(prompt)

    // 解析LLM返回的JSON
    let result
    try {
      // 尝试提取JSON部分
      const jsonMatch = llmResponse.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        result = JSON.parse(jsonMatch[0])
      } else {
        throw new Error('无法从LLM响应中提取JSON')
      }
    } catch (parseError) {
      console.error('解析LLM响应失败:', parseError)
      console.error('LLM原始响应:', llmResponse)

      // 如果解析失败，返回默认结果
      return res.status(500).json({
        success: false,
        message: '文本比对结果解析失败'
      })
    }

    // 验证结果格式
    if (typeof result.score !== 'number' || typeof result.detail !== 'string') {
      return res.status(500).json({
        success: false,
        message: '文本比对结果格式错误'
      })
    }

    res.json({
      success: true,
      score: result.score,
      characterConsistency: result.characterConsistency,
      detail: result.detail
    })

  } catch (error) {
    console.error('文本比对失败:', error)
    res.status(500).json({
      success: false,
      message: '文本比对失败: ' + error.message
    })
  }
})

// Excel批量比对接口
app.post('/api/excel-compare', upload.single('excel'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传Excel文件'
      })
    }

    // 读取Excel文件
    const workbook = XLSX.readFile(req.file.path)
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    // 将工作表转换为JSON数组
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

    if (data.length < 2) {
      // 清理上传的文件
      fs.unlinkSync(req.file.path)
      return res.status(400).json({
        success: false,
        message: 'Excel文件格式错误，至少需要表头和一行数据'
      })
    }

    // 检查表头格式
    const headers = data[0]
    const expectedHeaders = ['语言', '原文', 'ASR识别的文本', '文本一致性', '综合评分', '评分详情']
    const headerMap = {}

    // 建立表头映射
    expectedHeaders.forEach(expectedHeader => {
      const index = headers.findIndex(header =>
        header && header.toString().trim() === expectedHeader
      )
      if (index !== -1) {
        headerMap[expectedHeader] = index
      }
    })

    // 检查必要的列是否存在
    if (headerMap['原文'] === undefined || headerMap['ASR识别的文本'] === undefined) {
      fs.unlinkSync(req.file.path)
      return res.status(400).json({
        success: false,
        message: 'Excel文件必须包含"原文"和"ASR识别的文本"列'
      })
    }

    // 处理数据行
    const results = []
    const dataRows = data.slice(1) // 跳过表头

    for (let i = 0; i < dataRows.length; i++) {
      const row = dataRows[i]
      const originalText = row[headerMap['原文']] ? row[headerMap['原文']].toString().trim() : ''
      const recognizedText = row[headerMap['ASR识别的文本']] ? row[headerMap['ASR识别的文本']].toString().trim() : ''

      // 如果原文或ASR识别文本为空，跳过此行
      if (!originalText || !recognizedText) {
        results.push({
          rowIndex: i + 2, // Excel行号（从1开始，加上表头）
          skipped: true,
          reason: '原文或ASR识别文本为空'
        })
        continue
      }

      try {
        // 调用比对接口
        const compareResult = await compareTexts(originalText, recognizedText)
        results.push({
          rowIndex: i + 2,
          skipped: false,
          originalText,
          recognizedText,
          ...compareResult
        })
      } catch (error) {
        console.error(`第${i + 2}行比对失败:`, error)
        results.push({
          rowIndex: i + 2,
          skipped: true,
          reason: '比对失败: ' + error.message
        })
      }
    }

    // 清理上传的文件
    fs.unlinkSync(req.file.path)

    res.json({
      success: true,
      results,
      totalRows: dataRows.length,
      processedRows: results.filter(r => !r.skipped).length,
      skippedRows: results.filter(r => r.skipped).length
    })

  } catch (error) {
    console.error('Excel批量比对失败:', error)

    // 清理上传的文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path)
    }

    res.status(500).json({
      success: false,
      message: 'Excel处理失败: ' + error.message
    })
  }
})

// 生成Excel文件接口
app.post('/api/generate-excel', async (req, res) => {
  try {
    const { headers, data } = req.body

    if (!headers || !data || !Array.isArray(headers) || !Array.isArray(data)) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的表头和数据'
      })
    }

    // 创建新的工作簿
    const workbook = XLSX.utils.book_new()

    // 准备数据，表头作为第一行
    const worksheetData = [headers, ...data]

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData)

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

    // 生成Excel文件
    const fileName = `compare_result_${Date.now()}.xlsx`
    const filePath = path.join('uploads', fileName)

    XLSX.writeFile(workbook, filePath)

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`)

    // 发送文件
    res.sendFile(path.resolve(filePath), (err) => {
      if (err) {
        console.error('发送文件失败:', err)
      }
      // 删除临时文件
      setTimeout(() => {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath)
        }
      }, 5000) // 5秒后删除
    })

  } catch (error) {
    console.error('生成Excel失败:', error)
    res.status(500).json({
      success: false,
      message: '生成Excel失败: ' + error.message
    })
  }
})

// 文本比对辅助函数
async function compareTexts(originalText, recognizedText) {
  const prompt = `你是一个专业的文本比对评估专家。请比较以下两段文本并提供详细的分析：

原文：
${originalText}

语音识别文本：
${recognizedText}

请提供以下三个方面的评估：

1. 总体评分(score)：
- 90分以上：很完美，基本上跟原文差别不大
- 70分以上：及格，虽然跟原文有出入但是不影响理解
- 30分以上：不及格，已经影响到理解了
- 30分以下：完全牛头不对马嘴

2. 字数一致性(characterConsistency)：
- 计算方法：1 - (|原文字数 - 识别文本字数| / Math.max(原文字数, 识别文本字数)) * 100
- 结果应该是0-100之间的数字，表示字数匹配的百分比
- 两段文本字数完全相同时为100
- 字数差异越大，这个值越小

3. 详细分析(detail)：
- 描述文本差异的具体表现
- 指出主要的问题点
- 给出整体的比对结论

请按以下格式返回JSON结果（不要包含其他内容）：
{
  "score": 88,
  "characterConsistency": 95,
  "detail": "相比原文，语音识别后的文本在一些意思描述有点不清晰，但总体来说是正确的"
}
  `

  const llmResponse = await chatCompletion(prompt)

  // 解析LLM返回的JSON
  try {
    const jsonMatch = llmResponse.match(/\{[\s\S]*\}/)
    if (jsonMatch) {
      const result = JSON.parse(jsonMatch[0])

      // 验证结果格式
      if (typeof result.score !== 'number' || typeof result.detail !== 'string') {
        throw new Error('文本比对结果格式错误')
      }

      return {
        score: result.score,
        characterConsistency: result.characterConsistency,
        detail: result.detail
      }
    } else {
      throw new Error('无法从LLM响应中提取JSON')
    }
  } catch (parseError) {
    console.error('解析LLM响应失败:', parseError)
    console.error('LLM原始响应:', llmResponse)
    throw new Error('文本比对结果解析失败')
  }
}

// 健康检查接口
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: '服务器运行正常',
    timestamp: new Date().toISOString()
  })
})

// 启动服务器
app.listen(PORT, () => {
  console.log(`服务器已启动，端口: ${PORT}`)
  console.log(`API地址: http://localhost:${PORT}/api`)
  console.log('支持的接口:')
  console.log('  GET  /api/languages - 获取语言列表')
  console.log('  POST /api/translate - 文本翻译')
  console.log('  POST /api/tts - TTS生成（支持翻译模式）')
  console.log('  POST /api/compare - 文本比对')
  console.log('  POST /api/excel-compare - Excel批量比对')
  console.log('  POST /api/generate-excel - 生成Excel文件')
  console.log('  GET  /api/health - 健康检查')
})

module.exports = app

<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>语音生成与文本比对平台</title>
  </head>
  <body>
    <div id="app">
      <div class="container">
        <header class="header">
          <h1>语音生成与文本比对平台</h1>
        </header>

        <div class="tabs">
          <button class="tab-button active" data-tab="tts">语音生成</button>
          <button class="tab-button" data-tab="compare">文本比对</button>
          <button class="tab-button" data-tab="excel">Excel批量对比</button>
        </div>

        <div class="tab-content">
          <!-- TTS Tab -->
          <div id="tts-tab" class="tab-panel active">
            <div class="form-group">
              <label for="language-select">选择语言（可多选）：</label>
              <div id="language-select" class="language-multi-select">
                <div class="selected-languages">
                  <span class="placeholder">加载中...</span>
                </div>
                <div id="language-dropdown" class="language-dropdown" style="display: none;">
                  <!-- 语言选项将动态加载 -->
                </div>
              </div>
            </div>

            <div class="form-group">
              <label for="text-input">输入中文文本：</label>
              <textarea id="text-input" class="form-control" rows="4" placeholder="请输入中文文本，将自动翻译为所选语言并生成语音..."></textarea>
            </div>

            <div class="form-group">
              <button id="generate-btn" class="btn btn-primary">
                <span class="btn-text">批量生成语音</span>
              </button>
            </div>

            <!-- 进度条 -->
            <div id="progress-container" class="progress-container" style="display: none;">
              <div class="progress-label">生成进度：<span id="progress-text">0%</span></div>
              <div class="progress-bar">
                <div id="progress-fill" class="progress-fill"></div>
              </div>
            </div>

            <!-- 音频结果列表 -->
            <div id="audio-results" class="audio-results">
              <!-- 动态生成的音频结果将显示在这里 -->
            </div>
          </div>

          <!-- Compare Tab -->
          <div id="compare-tab" class="tab-panel">
            <div class="compare-container">
              <!-- 原文输入 -->
              <div class="form-group">
                <label for="original-text">原文：</label>
                <textarea id="original-text" class="form-control" rows="4" placeholder="请输入原文..."></textarea>
              </div>

              <!-- 语音识别文本列表 -->
              <div class="form-group">
                <div class="recognized-texts-header">
                  <label>语音识别文本：</label>
                  <button id="add-recognized-text" class="btn-add" title="添加更多文本">➕</button>
                </div>
                <div id="recognized-texts-list" class="recognized-texts-list">
                  <!-- 默认第一条 -->
                  <div class="recognized-text-item" data-index="0">
                    <div class="text-input-section">
                      <textarea class="form-control recognized-text-input" rows="3" placeholder="请输入语音识别后的文本..."></textarea>
                    </div>
                    <div class="result-section">
                      <!-- 比对结果将显示在这里 -->
                    </div>
                  </div>
                </div>
              </div>

              <div class="form-group">
                <button id="compare-btn" class="btn btn-primary">
                  <span class="btn-text">批量比对</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Excel Batch Compare Tab -->
          <div id="excel-tab" class="tab-panel">
            <div class="excel-container">
              <div class="excel-info">
                <h3>Excel批量对比说明</h3>
                <p>请上传包含以下表头的Excel文件：</p>
                <div class="excel-format-example">
                  <table class="format-table">
                    <thead>
                      <tr>
                        <th>语言</th>
                        <th>原文</th>
                        <th>ASR识别的文本</th>
                        <th>文本一致性</th>
                        <th>综合评分</th>
                        <th>评分详情</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>你好明天见</td>
                        <td>你好明天见吗？</td>
                        <td>（自动填充）</td>
                        <td>（自动填充）</td>
                        <td>（自动填充）</td>
                        <td>（自动填充）</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <p class="note">注意：只有"原文"和"ASR识别的文本"列是必需的，其他列将自动填充比对结果。</p>
              </div>

              <!-- 文件上传区域 -->
              <div class="form-group">
                <label for="excel-upload">选择Excel文件：</label>
                <div class="upload-area" id="upload-area">
                  <input type="file" id="excel-upload" accept=".xlsx,.xls" style="display: none;">
                  <div class="upload-content">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">
                      <div>点击选择Excel文件或拖拽文件到此处</div>
                      <div class="upload-hint">支持 .xlsx 和 .xls 格式，最大10MB</div>
                    </div>
                  </div>
                </div>
                <div id="file-info" class="file-info" style="display: none;">
                  <span class="file-name"></span>
                  <button class="btn-remove-file" title="移除文件">×</button>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="form-group">
                <button id="start-compare-btn" class="btn btn-primary" disabled>
                  <span class="btn-text">开始校对</span>
                </button>
              </div>

              <!-- 进度条 -->
              <div id="excel-progress-container" class="progress-container" style="display: none;">
                <div class="progress-label">校对进度：<span id="excel-progress-text">0%</span></div>
                <div class="progress-bar">
                  <div id="excel-progress-fill" class="progress-fill"></div>
                </div>
                <div class="progress-details">
                  <span id="progress-details-text">准备中...</span>
                </div>
              </div>

              <!-- 结果显示 -->
              <div id="excel-results" class="excel-results" style="display: none;">
                <div class="results-header">
                  <h3>校对完成</h3>
                  <div class="results-summary">
                    <span id="results-summary-text"></span>
                  </div>
                </div>
                <div class="results-actions">
                  <button id="download-excel-btn" class="btn btn-success">
                    <span class="btn-text">下载结果Excel</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>

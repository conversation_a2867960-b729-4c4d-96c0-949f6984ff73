(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))c(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&c(a)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function c(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();const h="/api_compare/api",N=document.querySelectorAll(".tab-button"),_=document.querySelectorAll(".tab-panel"),b=document.querySelector("#language-select"),F=document.querySelector("#text-input"),S=document.querySelector("#generate-btn"),O=document.querySelector("#progress-container"),J=document.querySelector("#progress-fill"),Q=document.querySelector("#progress-text"),$=document.querySelector("#audio-results"),K=document.querySelector("#original-text"),k=document.querySelector("#recognized-texts-list"),V=document.querySelector("#add-recognized-text"),E=document.querySelector("#compare-btn"),m=document.querySelector("#upload-area"),T=document.querySelector("#excel-upload"),I=document.querySelector("#file-info"),X=document.querySelector(".file-name"),G=document.querySelector(".btn-remove-file"),f=document.querySelector("#start-compare-btn"),q=document.querySelector("#excel-progress-container"),B=document.querySelector("#excel-progress-fill"),R=document.querySelector("#excel-progress-text"),C=document.querySelector("#progress-details-text"),w=document.querySelector("#excel-results"),W=document.querySelector("#results-summary-text"),Y=document.querySelector("#download-excel-btn");let d=[],H=[],A=0,v=null,g=null;const Z=10;class ee{queue=[];running=0;async add(t){this.queue.push(t),await this.runNext()}async runNext(){if(this.running>=Z||this.queue.length===0)return;this.running++;const t=this.queue.shift();try{await t()}finally{this.running--,await this.runNext()}}}const te=new ee;N.forEach(e=>{e.addEventListener("click",()=>{const t=e.getAttribute("data-tab");N.forEach(n=>n.classList.remove("active")),_.forEach(n=>n.classList.remove("active")),e.classList.add("active"),document.querySelector(`#${t}-tab`)?.classList.add("active")})});async function ne(){try{H=await(await fetch(`${h}/languages`)).json(),se(),L()}catch(e){console.error("加载语言列表失败:",e);const t=b.querySelector(".placeholder");t&&(t.textContent="加载失败，请刷新重试")}}function se(){const e=b.querySelector(".selected-languages"),t=document.querySelector("#language-dropdown");t.innerHTML="";const n=document.createElement("div");n.className="language-search-container";const c=document.createElement("input");c.className="language-search-input",c.placeholder="搜索语言...",n.appendChild(c);const s=document.createElement("div");s.className="select-all-container";const o=document.createElement("button");o.className="select-all-btn",o.textContent="全选";const a=document.createElement("button");a.className="deselect-all-btn",a.textContent="全不选",s.appendChild(o),s.appendChild(a),n.appendChild(s),t.appendChild(n);const r=document.createElement("div");r.className="language-options-container",t.appendChild(r);function l(i=""){r.innerHTML="",H.filter(u=>u.name.toLowerCase().includes(i.toLowerCase())).forEach(u=>{const y=document.createElement("div");y.className="language-option",y.textContent=u.name,y.dataset.code=u.code,y.addEventListener("click",()=>{D(u)}),r.appendChild(y)})}l(),c.addEventListener("input",i=>{const u=i.target.value;l(u),x()}),o.addEventListener("click",i=>{i.stopPropagation(),[...r.querySelectorAll(".language-option")].map(p=>({code:p.dataset.code,name:p.textContent})).forEach(p=>{d.some(j=>j.code===p.code)||d.push(p)}),L(),x()}),a.addEventListener("click",i=>{i.stopPropagation();const y=[...r.querySelectorAll(".language-option")].map(p=>p.dataset.code);d=d.filter(p=>!y.includes(p.code)),L(),x()}),e.addEventListener("click",()=>{const i=t.style.display==="block";t.style.display=i?"none":"block",e.classList.toggle("active",!i)}),document.addEventListener("click",i=>{b.contains(i.target)||(t.style.display="none",e.classList.remove("active"))})}function D(e){const t=d.findIndex(n=>n.code===e.code);t>-1?d.splice(t,1):d.push(e),L(),x()}function L(){const e=b.querySelector(".selected-languages");d.length===0?e.innerHTML='<span class="placeholder">请选择语言（可多选）</span>':(e.innerHTML="",d.forEach(t=>{const n=document.createElement("div");n.className="language-tag",n.innerHTML=`
        <span>${t.name}</span>
        <span class="remove" data-code="${t.code}">×</span>
      `,n.querySelector(".remove").addEventListener("click",s=>{s.stopPropagation(),D(t)}),e.appendChild(n)}))}function x(){document.querySelector("#language-dropdown").querySelectorAll(".language-option").forEach(n=>{const s=n.dataset.code,o=d.some(a=>a.code===s);n.classList.toggle("selected",o)})}S.addEventListener("click",async()=>{const e=F.value.trim();if(d.length===0){alert("请至少选择一种语言");return}if(!e){alert("请输入中文文本");return}O.style.display="block",$.innerHTML="",S.disabled=!0;const t=d.length;let n=0;function c(){const o=Math.round(n/t*100);J.style.width=`${o}%`,Q.textContent=`${o}%`}c();const s=d.map(async o=>{try{const r=await(await fetch(`${h}/tts`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({language:o.code,text:e,needTranslation:!0})})).json();r.success&&r.audioBase64?oe(o,r.translatedText||e,r.audioBase64):P(o,r.message||"生成失败")}catch(a){console.error(`${o.name} TTS生成失败:`,a),P(o,"网络错误")}finally{n++,c()}});await Promise.all(s),S.disabled=!1,setTimeout(()=>{O.style.display="none"},3e3)});function oe(e,t,n){const c=ce(n,"audio/mpeg"),s=URL.createObjectURL(c),o=document.createElement("div");o.className="audio-result-item",o.innerHTML=`
    <div class="audio-result-header">
      <div class="language-name">${e.name}</div>
    </div>
    <div class="audio-result-content">
      <div class="translated-text-section">
        <div class="label">翻译后的文本：</div>
        <div class="translated-text-content">
          ${t}
          <button class="btn-copy" title="复制翻译文本">📋</button>
        </div>
      </div>
      <div class="audio-controls">
        <audio controls class="audio-player-small">
          <source src="${s}" type="audio/mpeg">
          您的浏览器不支持音频播放。
        </audio>
        <button class="btn btn-secondary download-btn">下载音频</button>
      </div>
    </div>
  `;const a=o.querySelector(".btn-copy");a.addEventListener("click",async()=>{try{await navigator.clipboard.writeText(t);const l=a.textContent;a.textContent="✅",setTimeout(()=>{a.textContent=l},1e3)}catch(l){console.error("复制失败:",l),alert("复制失败，请手动选择文本复制")}}),o.querySelector(".download-btn").addEventListener("click",()=>{const l=document.createElement("a");l.href=s,l.download=`tts_${e.code}_${Date.now()}.mp3`,l.click()}),$.appendChild(o)}function P(e,t){const n=document.createElement("div");n.className="audio-result-item",n.style.background="linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)",n.style.borderColor="#f5c6cb",n.innerHTML=`
    <div class="audio-result-header">
      <div class="language-name" style="color: #721c24;">${e.name}</div>
    </div>
    <div class="audio-result-content">
      <div style="color: #721c24; text-align: center; padding: 20px;">
        ❌ ${t}
      </div>
    </div>
  `,$.appendChild(n)}function ce(e,t){const n=atob(e),c=new Array(n.length);for(let o=0;o<n.length;o++)c[o]=n.charCodeAt(o);const s=new Uint8Array(c);return new Blob([s],{type:t})}V.addEventListener("click",()=>{A++;const e=U(A);k.appendChild(e)});function U(e){const t=document.createElement("div");return t.className="recognized-text-item",t.dataset.index=e.toString(),t.innerHTML=`
    <div class="text-input-section">
      ${e>0?'<button class="btn-remove" title="删除此项">➖</button>':""}
      <textarea class="form-control recognized-text-input" rows="3" placeholder="请输入语音识别后的文本..."></textarea>
    </div>
    <div class="result-section">
      <span style="color: #6c757d;">等待比对...</span>
    </div>
  `,e>0&&t.querySelector(".btn-remove").addEventListener("click",()=>{t.remove()}),t}async function ae(e,t,n,c){const s=e.querySelector(".result-section");s.className="result-section loading",s.innerHTML='<span style="color: #856404;">比对中...</span>';try{const a=await(await fetch(`${h}/compare`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({originalText:t,recognizedText:n})})).json();if(a.success&&typeof a.score=="number"&&a.detail){s.className="result-section completed";let r="score-poor";a.score>=90?r="score-excellent":a.score>=70?r="score-good":a.score>=30&&(r="score-fair");let l="consistency-low";const i=a.characterConsistency??0;i>=90?l="consistency-high":i>=70?l="consistency-medium":i>=30?l="consistency-low":l="consistency-very-low",s.innerHTML=`
        <div class="compare-result-inline">
          <div class="score-display ${r}">${a.score}</div>
          <div class="character-consistency ${l}">
            <span class="consistency-label">文本一致性：</span>
            <span class="consistency-value">${a.characterConsistency??"计算中..."}${a.characterConsistency?"%":""}</span>
          </div>
          <div class="detail-display">${a.detail}</div>
        </div>
      `}else s.className="result-section",s.innerHTML=`<span style="color: #dc3545;">❌ ${a.message||"比对失败"}</span>`}catch(o){console.error(`文本比对失败 (${c}):`,o),s.className="result-section",s.innerHTML='<span style="color: #dc3545;">❌ 网络错误</span>'}}E.addEventListener("click",async()=>{const e=K.value.trim(),t=k.querySelectorAll(".recognized-text-item");if(!e){alert("请输入原文");return}const n=[];if(t.forEach((s,o)=>{const r=s.querySelector(".recognized-text-input").value.trim();r&&n.push({element:s,text:r,index:o})}),n.length===0){alert("请至少输入一条语音识别文本");return}E.disabled=!0;const c=[];for(const s of n)c.push(te.add(async()=>{await ae(s.element,e,s.text,s.index)}));await Promise.all(c),E.disabled=!1});function re(){m.addEventListener("click",()=>{T.click()}),m.addEventListener("dragover",e=>{e.preventDefault(),m.classList.add("dragover")}),m.addEventListener("dragleave",()=>{m.classList.remove("dragover")}),m.addEventListener("drop",e=>{e.preventDefault(),m.classList.remove("dragover");const t=e.dataTransfer?.files;t&&t.length>0&&M(t[0])}),T.addEventListener("change",e=>{const t=e.target.files;t&&t.length>0&&M(t[0])}),G.addEventListener("click",()=>{le()}),f.addEventListener("click",()=>{v&&ie()}),Y.addEventListener("click",()=>{g&&ue()})}function M(e){if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(e.type)){alert("请选择Excel文件（.xlsx 或 .xls）");return}if(e.size>10*1024*1024){alert("文件大小不能超过10MB");return}v=e,X.textContent=e.name,I.style.display="flex",m.style.display="none",f.disabled=!1,w.style.display="none"}function le(){v=null,T.value="",I.style.display="none",m.style.display="block",f.disabled=!0,w.style.display="none",q.style.display="none"}async function ie(){if(v){f.disabled=!0,q.style.display="block",w.style.display="none",B.style.width="0%",R.textContent="0%",C.textContent="正在上传文件...";try{const e=new FormData;e.append("excel",v);const n=await(await fetch(`${h}/excel-compare`,{method:"POST",body:e})).json();if(n.success&&n.results)g=n,B.style.width="100%",R.textContent="100%",C.textContent="校对完成！",de(n),setTimeout(()=>{q.style.display="none"},3e3);else throw new Error(n.message||"处理失败")}catch(e){console.error("Excel批量比对失败:",e),C.textContent="处理失败: "+e.message,alert("Excel处理失败: "+e.message)}finally{f.disabled=!1}}}function de(e){if(!e.results)return;const t=`
    总计 ${e.totalRows} 行数据，
    成功处理 ${e.processedRows} 行，
    跳过 ${e.skippedRows} 行
  `;W.textContent=t,w.style.display="block"}async function ue(){if(!(!g||!g.results))try{const e=["语言","原文","ASR识别的文本","文本一致性","综合评分","评分详情"],t=[];g.results.forEach(c=>{c.skipped?t.push(["",c.originalText||"",c.recognizedText||"",c.reason||"跳过","",""]):t.push(["",c.originalText||"",c.recognizedText||"",c.characterConsistency?`${c.characterConsistency}%`:"",c.score||"",c.detail||""])});const n=await fetch(`${h}/generate-excel`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({headers:e,data:t})});if(n.ok){const c=await n.blob(),s=window.URL.createObjectURL(c),o=document.createElement("a");o.href=s,o.download=`excel_compare_result_${Date.now()}.xlsx`,o.click(),window.URL.revokeObjectURL(s)}else throw new Error("生成Excel文件失败")}catch(e){console.error("下载Excel失败:",e),alert("下载失败: "+e.message)}}function z(){ne();const e=U(0);k.appendChild(e),re()}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",z):z();
